import { Trash2, TrendingUp, AlertTriangle, ExternalLink, RefreshCw, Activity, TrendingDown, ChevronDown, ChevronRight, Folder, FolderOpen, GripVertical, Edit3, Check, X } from 'lucide-react';
import { Stock } from '@/types';
import { useBatchStockData } from '@/hooks/useStockData';
import { detectVPattern } from '@/utils/patternDetection';
import { useMemo, useState, useCallback } from 'react';
import { MiniFlowChart } from '@/components/DataDisplay/MiniFlowChart';
import { formatMoneyAuto } from '@/utils/formatters';
import { useStockGroups } from '@/hooks/useStockGroups';
import { useStockList } from '@/hooks/useStockList';
import { GroupCollapseState } from '@/types/stock';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
  DragOverEvent,
} from '@dnd-kit/core';
import {
  useDraggable,
  useDroppable,
} from '@dnd-kit/core';

interface StockListProps {
  stocks: Stock[];
  onRemoveStock: (code: string) => void;
  onSelectStock?: (code: string) => void;
  selectedStock?: string | null;
  /** 是否显示实时监控数据 */
  showRealTimeData?: boolean;
  /** 是否全屏显示 */
  isFullScreen?: boolean;
  /** 是否启用分组显示 */
  enableGrouping?: boolean;
}

export function StockList({
  stocks,
  onRemoveStock,
  onSelectStock,
  selectedStock,
  showRealTimeData = false,
  isFullScreen = false,
  enableGrouping = true
}: StockListProps) {
  // 获取股票代码列表
  const stockCodes = stocks.map(s => s.code);

  // 分组管理hooks
  const { groups } = useStockGroups();
  const { getStocksByGroup, moveStockToGroup } = useStockList();

  // 分组折叠状态管理
  const [groupCollapseState, setGroupCollapseState] = useState<GroupCollapseState>({});

  // 拖拽状态管理
  const [activeStock, setActiveStock] = useState<Stock | null>(null);
  const [dragOverGroupId, setDragOverGroupId] = useState<string | null>(null);

  // 拖拽传感器配置
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 8px的移动距离才开始拖拽，避免误触
      },
    })
  );

  // 批量获取股票数据（仅在显示实时数据时）
  const {
    results,
    isLoading: dataLoading,
    isFetching,
    refetch
  } = useBatchStockData(stockCodes, 20, {
    refetchInterval: showRealTimeData ? 60000 : undefined, // 60秒自动刷新
    enabled: showRealTimeData && stockCodes.length > 0
  });

  // 处理股票数据，集成V字型识别和实时数据
  const stocksWithData = useMemo(() => {
    if (!showRealTimeData || !results || Object.keys(results).length === 0) {
      return stocks.map(stock => ({ ...stock, data: null, hasVPattern: false, latestFlow: 0, change24h: 0 }));
    }

    return stocks.map(stock => {
      const data = results[stock.code];
      const hasVPattern = data?.klines ? detectVPattern(data.klines).hasVPattern : false;

      // 计算最新流入和变化
      const latestFlow = data?.klines?.[data.klines.length - 1]?.mainNetInflow || 0;
      const change24h = data?.klines && data.klines.length >= 2
        ? data.klines[data.klines.length - 1].mainNetInflow - data.klines[data.klines.length - 2].mainNetInflow
        : 0;

      return {
        ...stock,
        data,
        hasVPattern,
        latestFlow,
        change24h,
      };
    });
  }, [stocks, results, showRealTimeData]);

  // 分组处理逻辑
  const groupedStocks = useMemo(() => {
    if (!enableGrouping || groups.length === 0) {
      return null;
    }

    const groupedData = groups.map(group => {
      const groupStocks = getStocksByGroup(group.id)
        .map(stock => stocksWithData.find(s => s.code === stock.code))
        .filter((stock): stock is NonNullable<typeof stock> => Boolean(stock));

      return {
        group,
        stocks: groupStocks,
        isCollapsed: groupCollapseState[group.id] || false
      };
    });

    return groupedData;
  }, [enableGrouping, groups, getStocksByGroup, stocksWithData, groupCollapseState]);

  // 切换分组折叠状态
  const toggleGroupCollapse = useCallback((groupId: string) => {
    setGroupCollapseState(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  }, []);

  // 拖拽开始事件
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    const stockCode = active.id as string;
    const stock = stocksWithData.find(s => s.code === stockCode);
    if (stock) {
      setActiveStock(stock);
    }
  }, [stocksWithData]);

  // 拖拽悬停事件
  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { over } = event;
    if (over) {
      const overId = over.id as string;
      // 检查是否悬停在分组上
      if (overId.startsWith('group-')) {
        const groupId = overId.replace('group-', '');
        setDragOverGroupId(groupId);
      } else {
        setDragOverGroupId(null);
      }
    } else {
      setDragOverGroupId(null);
    }
  }, []);

  // 拖拽结束事件
  const handleDragEnd = useCallback(async (event: DragEndEvent) => {
    const { active, over } = event;

    setActiveStock(null);
    setDragOverGroupId(null);

    if (!over) return;

    const stockCode = active.id as string;
    const overId = over.id as string;

    // 检查是否拖拽到分组上
    if (overId.startsWith('group-')) {
      const targetGroupId = overId.replace('group-', '');

      try {
        const result = await moveStockToGroup({
          stockCode,
          targetGroupId: targetGroupId === 'default' ? null : targetGroupId
        });

        if (!result.success) {
          console.error('移动股票到分组失败:', result.message);
        }
      } catch (error) {
        console.error('拖拽移动股票失败:', error);
      }
    }
  }, []);

  if (stocks.length === 0) {
    return (
      <div className={`text-center ${isFullScreen ? 'py-16' : 'py-8'}`}>
        <TrendingUp className={`${isFullScreen ? 'w-20 h-20' : 'w-12 h-12'} text-gray-300 mx-auto mb-3`} />
        <p className={`text-gray-500 mb-2 ${isFullScreen ? 'text-lg' : ''}`}>暂无股票代码</p>
        <p className={`${isFullScreen ? 'text-base' : 'text-sm'} text-gray-400`}>请添加股票代码开始监控</p>
      </div>
    );
  }

  // 渲染分组显示
  if (enableGrouping && groupedStocks) {
    return (
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="space-y-2 h-full flex flex-col">
        {/* 列表头部 */}
        <div className={`flex items-center justify-between ${isFullScreen ? 'text-base' : 'text-sm'} text-gray-600 pb-2 border-b border-gray-200`}>
          <div className="flex items-center gap-2">
            <span>股票代码 ({stocks.length})</span>
            {showRealTimeData && (
              <>
                {isFetching && (
                  <RefreshCw className={`${isFullScreen ? 'w-4 h-4' : 'w-3 h-3'} animate-spin text-blue-500`} />
                )}
                <button
                  onClick={() => refetch()}
                  className={`${isFullScreen ? 'text-sm' : 'text-xs'} text-gray-500 hover:text-gray-700 transition-colors`}
                  disabled={isFetching}
                  title="刷新实时数据"
                >
                  <Activity className={`${isFullScreen ? 'w-4 h-4' : 'w-3 h-3'}`} />
                </button>
              </>
            )}
          </div>
          <span>操作</span>
        </div>

        {/* 分组股票列表 */}
        <div className="flex-1 overflow-y-auto">
          <div className="space-y-3">
            {groupedStocks.map(({ group, stocks: groupStocks, isCollapsed }) => (
              <GroupDropZone
                key={group.id}
                group={group}
                isCollapsed={isCollapsed}
                groupStocks={groupStocks}
                isDragOver={dragOverGroupId === group.id}
                onToggleCollapse={() => toggleGroupCollapse(group.id)}
                selectedStock={selectedStock}
                onSelectStock={onSelectStock}
                onRemoveStock={onRemoveStock}
                showRealTimeData={showRealTimeData}
                isFullScreen={isFullScreen}
                groups={groups}
                onMoveToGroup={async (stockCode, groupId) => {
                  try {
                    const result = await moveStockToGroup({
                      stockCode,
                      targetGroupId: groupId || null
                    });
                    if (!result.success) {
                      console.error('移动股票到分组失败:', result.message);
                    }
                  } catch (error) {
                    console.error('移动股票到分组失败:', error);
                  }
                }}
              />

            ))}
          </div>
        </div>

        {/* 批量操作和状态信息 */}
        {stocks.length > 1 && (
          <div className="pt-3 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <button
                onClick={() => {
                  if (window.confirm('确定要清空所有股票代码吗？')) {
                    stocks.forEach(stock => onRemoveStock(stock.code));
                  }
                }}
                className="text-sm text-danger-600 hover:text-danger-700 flex items-center gap-1"
              >
                <AlertTriangle className="w-4 h-4" />
                清空所有
              </button>

              {showRealTimeData && (
                <div className="text-xs text-gray-500">
                  {dataLoading ? '加载中...' : `实时监控已启用`}
                </div>
              )}
            </div>
          </div>
        )}

        {/* 拖拽覆盖层 */}
        <DragOverlay>
          {activeStock ? (
            <div className="bg-white border border-gray-300 rounded-lg p-2 shadow-lg opacity-90">
              <div className="flex items-center gap-2">
                <GripVertical className="w-4 h-4 text-gray-400" />
                <span className="font-medium text-gray-900">{activeStock.code}</span>
                <span className="text-sm text-gray-500">{activeStock.name}</span>
              </div>
            </div>
          ) : null}
        </DragOverlay>
      </div>
      </DndContext>
    );
  }

  // 传统的非分组显示
  return (
    <div className="space-y-2 h-full flex flex-col">
      {/* 列表头部 */}
      <div className={`flex items-center justify-between ${isFullScreen ? 'text-base' : 'text-sm'} text-gray-600 pb-2 border-b border-gray-200`}>
        <div className="flex items-center gap-2">
          <span>股票代码 ({stocks.length})</span>
          {showRealTimeData && (
            <>
              {isFetching && (
                <RefreshCw className={`${isFullScreen ? 'w-4 h-4' : 'w-3 h-3'} animate-spin text-blue-500`} />
              )}
              <button
                onClick={() => refetch()}
                className={`${isFullScreen ? 'text-sm' : 'text-xs'} text-gray-500 hover:text-gray-700 transition-colors`}
                disabled={isFetching}
                title="刷新实时数据"
              >
                <Activity className={`${isFullScreen ? 'w-4 h-4' : 'w-3 h-3'}`} />
              </button>
            </>
          )}
        </div>
        <span>操作</span>
      </div>

      {/* 股票列表 - 响应式多列网格布局 */}
      <div className="flex-1 overflow-y-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-2">
          {stocksWithData.map((stock) => (
            <StockListItem
              key={stock.code}
              stock={stock}
              isSelected={selectedStock === stock.code}
              onSelect={onSelectStock}
              onRemove={onRemoveStock}
              showRealTimeData={showRealTimeData}
              stockData={stock.data}
              hasVPattern={stock.hasVPattern}
              latestFlow={stock.latestFlow}
              change24h={stock.change24h}
              isFullScreen={isFullScreen}
              groups={groups}
              onMoveToGroup={async (stockCode, groupId) => {
                try {
                  const result = await moveStockToGroup({
                    stockCode,
                    targetGroupId: groupId || null
                  });
                  if (!result.success) {
                    console.error('移动股票到分组失败:', result.message);
                  }
                } catch (error) {
                  console.error('移动股票到分组失败:', error);
                }
              }}
            />
          ))}
        </div>
      </div>
      
      {/* 批量操作和状态信息 */}
      {stocks.length > 1 && (
        <div className="pt-3 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <button
              onClick={() => {
                if (window.confirm('确定要清空所有股票代码吗？')) {
                  stocks.forEach(stock => onRemoveStock(stock.code));
                }
              }}
              className="text-sm text-danger-600 hover:text-danger-700 flex items-center gap-1"
            >
              <AlertTriangle className="w-4 h-4" />
              清空所有
            </button>

            {showRealTimeData && (
              <div className="text-xs text-gray-500">
                {dataLoading ? '加载中...' : `实时监控已启用`}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

interface StockListItemProps {
  stock: Stock;
  isSelected?: boolean;
  onSelect?: (code: string) => void;
  onRemove: (code: string) => void;
  showRealTimeData?: boolean;
  stockData?: any;
  hasVPattern?: boolean;
  latestFlow?: number;
  change24h?: number;
  isFullScreen?: boolean;
  groups?: any[];
  onMoveToGroup?: (stockCode: string, groupId: string) => void;
}

function StockListItem({
  stock,
  isSelected,
  onSelect,
  onRemove,
  showRealTimeData = false,
  stockData,
  hasVPattern = false,
  latestFlow = 0,
  change24h = 0,
  isFullScreen = false,
  groups = [],
  onMoveToGroup
}: StockListItemProps) {
  const [isEditingGroup, setIsEditingGroup] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState(stock.groupId || '');
  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm(`确定要删除股票 ${stock.code} 吗？`)) {
      onRemove(stock.code);
    }
  };

  const handleSelect = () => {
    if (onSelect) {
      onSelect(stock.code);
    }
  };

  const handleViewFlow = (e: React.MouseEvent) => {
    e.stopPropagation();
    const url = `https://data.eastmoney.com/zjlx/${stock.code}.html`;
    window.open(url, '_blank');
  };

  // 处理分组编辑
  const handleEditGroup = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsEditingGroup(true);
  };

  // 保存分组变更
  const handleSaveGroup = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onMoveToGroup && selectedGroupId !== stock.groupId) {
      onMoveToGroup(stock.code, selectedGroupId);
    }
    setIsEditingGroup(false);
  };

  // 取消分组编辑
  const handleCancelEditGroup = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedGroupId(stock.groupId || '');
    setIsEditingGroup(false);
  };

  // 获取中国股市颜色（红涨绿跌）
  const getChineseStockColor = (value: number) => {
    if (value > 0) {
      return 'text-red-600'; // 红色 - 上涨/流入
    } else if (value < 0) {
      return 'text-green-600'; // 绿色 - 下跌/流出
    } else {
      return 'text-gray-600'; // 灰色 - 无变化
    }
  };

  return (
    <div
      className={`
        group flex flex-col ${isFullScreen ? 'p-3' : 'p-2'} rounded-lg border transition-all duration-200
        ${isSelected
          ? 'border-primary-500 bg-primary-50'
          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
        }
        ${onSelect ? 'cursor-pointer' : ''}
        min-h-[80px] w-full
      `}
      onClick={handleSelect}
    >
      {/* 第一行：股票代码和名称 + 选中指示器 */}
      <div className="flex items-center justify-between mb-1">
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <span className={`${isFullScreen ? 'text-sm' : 'text-xs'} font-medium ${
            isSelected ? 'text-primary-700' : 'text-gray-900'
          } truncate`}>
            {stock.name}
          </span>
          <span className={`${isFullScreen ? 'text-xs' : 'text-xs'} text-gray-500 font-mono flex-shrink-0`}>
            {stock.code}
          </span>
        </div>
        {/* 选中指示器 */}
        {isSelected && (
          <div className="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0"></div>
        )}
      </div>

      {/* 第二行：实时监控数据 */}
      {showRealTimeData && stockData && stockData.klines && (
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center gap-2">
            {/* 主力净流入 */}
            <span
              className={`font-bold ${isFullScreen ? 'text-xs' : 'text-xs'} ${getChineseStockColor(latestFlow)}`}
            >
              {formatMoneyAuto(latestFlow)}
            </span>

            {/* 24小时变化 */}
            {change24h !== 0 && (
              <div className="flex items-center gap-1">
                {change24h > 0 ? (
                  <TrendingUp className="w-3 h-3 text-red-500" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-green-500" />
                )}
                <span
                  className={`text-xs font-medium ${getChineseStockColor(change24h)}`}
                >
                  {change24h > 0 ? '+' : ''}{formatMoneyAuto(change24h)}
                </span>
              </div>
            )}

            {/* V字形模式指示器 */}
            {hasVPattern && (
              <div className="flex items-center gap-1">
                <Activity className="w-3 h-3 text-red-500" />
                <span className="text-xs text-red-600 font-medium">V型</span>
              </div>
            )}
          </div>

          {/* 迷你图表 */}
          <div className="w-12 h-5 flex-shrink-0">
            <MiniFlowChart
              klines={stockData.klines}
              height={20}
              showVPattern={true}
            />
          </div>
        </div>
      )}

      {/* 无数据状态 */}
      {showRealTimeData && (!stockData || !stockData.klines) && (
        <div className="flex items-center text-gray-400 mb-1">
          <span className="text-xs">加载中...</span>
        </div>
      )}

      {/* 第三行：操作按钮和分组编辑 */}
      <div className="flex items-center justify-between">
        {/* 分组编辑功能 */}
        {groups.length > 0 && isEditingGroup ? (
          <div className="flex items-center gap-1">
            <select
              value={selectedGroupId}
              onChange={(e) => setSelectedGroupId(e.target.value)}
              className="text-xs border border-gray-300 rounded px-1 py-0.5 bg-white"
              onClick={(e) => e.stopPropagation()}
            >
              <option value="">默认分组</option>
              {groups.map((group) => (
                <option key={group.id} value={group.id}>
                  {group.name}
                </option>
              ))}
            </select>
            <button
              onClick={handleSaveGroup}
              className="p-0.5 text-green-600 hover:text-green-700"
              title="保存"
            >
              <Check className="w-3 h-3" />
            </button>
            <button
              onClick={handleCancelEditGroup}
              className="p-0.5 text-gray-400 hover:text-gray-600"
              title="取消"
            >
              <X className="w-3 h-3" />
            </button>
          </div>
        ) : (
          <div></div>
        )}

        {/* 操作按钮组 */}
        <div className="flex items-center gap-1">
          {/* 编辑分组按钮（仅在有分组时显示） */}
          {groups.length > 0 && !isEditingGroup && (
            <button
              onClick={handleEditGroup}
              className="p-1 text-gray-400 hover:text-blue-600 opacity-0 group-hover:opacity-100 transition-opacity"
              title="编辑分组"
            >
              <Edit3 className="w-3 h-3" />
            </button>
          )}

          {/* 查看资金流向按钮 */}
          <button
            onClick={handleViewFlow}
            className="p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200"
            title="查看资金流向"
          >
            <ExternalLink className="w-3 h-3" />
          </button>

          {/* 删除按钮 */}
          <button
            onClick={handleRemove}
            className="p-1 text-gray-400 hover:text-danger-600 transition-colors duration-200"
            title="删除股票"
          >
            <Trash2 className="w-3 h-3" />
          </button>
        </div>
      </div>
    </div>
  );
}

// 分组拖拽目标区域组件
interface GroupDropZoneProps {
  group: any;
  isCollapsed: boolean;
  groupStocks: any[];
  isDragOver: boolean;
  onToggleCollapse: () => void;
  selectedStock?: string | null;
  onSelectStock?: (code: string) => void;
  onRemoveStock: (code: string) => void;
  showRealTimeData?: boolean;
  isFullScreen?: boolean;
  groups?: any[];
  onMoveToGroup?: (stockCode: string, groupId: string) => void;
}

function GroupDropZone({
  group,
  isCollapsed,
  groupStocks,
  isDragOver,
  onToggleCollapse,
  selectedStock,
  onSelectStock,
  onRemoveStock,
  showRealTimeData = false,
  isFullScreen = false,
  groups = [],
  onMoveToGroup
}: GroupDropZoneProps) {
  const { setNodeRef } = useDroppable({
    id: `group-${group.id}`,
  });

  return (
    <div
      ref={setNodeRef}
      className={`border rounded-lg transition-colors ${
        isDragOver
          ? 'border-blue-500 bg-blue-50'
          : 'border-gray-200'
      }`}
    >
      {/* 分组头部 */}
      <div
        className={`flex items-center justify-between p-3 bg-gray-50 rounded-t-lg cursor-pointer hover:bg-gray-100 transition-colors ${
          isDragOver ? 'bg-blue-100' : ''
        }`}
        onClick={onToggleCollapse}
      >
        <div className="flex items-center gap-2">
          {isCollapsed ? (
            <ChevronRight className="w-4 h-4 text-gray-500" />
          ) : (
            <ChevronDown className="w-4 h-4 text-gray-500" />
          )}
          <div
            className="w-3 h-3 rounded-full border border-gray-300"
            style={{ backgroundColor: group.color }}
          />
          <span className="font-medium text-gray-900">{group.name}</span>
          <span className="text-sm text-gray-500">({groupStocks.length})</span>
          {group.isDefault && (
            <span className="text-xs bg-gray-200 text-gray-600 px-2 py-0.5 rounded">
              默认
            </span>
          )}
          {isDragOver && (
            <span className="text-xs bg-blue-200 text-blue-700 px-2 py-0.5 rounded">
              拖拽到此分组
            </span>
          )}
        </div>
        {isCollapsed ? (
          <Folder className="w-4 h-4 text-gray-400" />
        ) : (
          <FolderOpen className="w-4 h-4 text-gray-400" />
        )}
      </div>

      {/* 分组内容 */}
      {!isCollapsed && (
        <div className="p-3 pt-0">
          {groupStocks.length === 0 ? (
            <div className={`text-center py-4 text-gray-500 ${
              isDragOver ? 'bg-blue-50 border-2 border-dashed border-blue-300 rounded' : ''
            }`}>
              <span className="text-sm">
                {isDragOver ? '释放以添加到此分组' : '该分组暂无股票'}
              </span>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-2">
              {groupStocks.map((stock) => {
                // 确保stock不为undefined（已经在filter中处理过）
                if (!stock) return null;

                return (
                  <DraggableStockItem
                    key={stock.code}
                    stock={stock}
                    isSelected={selectedStock === stock.code}
                    onSelect={onSelectStock}
                    onRemove={onRemoveStock}
                    showRealTimeData={showRealTimeData}
                    stockData={stock.data}
                    hasVPattern={stock.hasVPattern}
                    latestFlow={stock.latestFlow}
                    change24h={stock.change24h}
                    isFullScreen={isFullScreen}
                    groups={groups}
                    onMoveToGroup={onMoveToGroup}
                  />
                );
              })}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

// 可拖拽的股票项组件
interface DraggableStockItemProps {
  stock: any;
  isSelected?: boolean;
  onSelect?: (code: string) => void;
  onRemove: (code: string) => void;
  showRealTimeData?: boolean;
  stockData?: any;
  hasVPattern?: boolean;
  latestFlow?: number;
  change24h?: number;
  isFullScreen?: boolean;
  groups?: any[];
  onMoveToGroup?: (stockCode: string, groupId: string) => void;
}

function DraggableStockItem(props: DraggableStockItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: props.stock.code,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`${isDragging ? 'opacity-50' : ''}`}
      {...attributes}
    >
      <div className="relative">
        {/* 拖拽手柄 */}
        <div
          {...listeners}
          className="absolute top-2 left-2 z-10 p-1 opacity-0 hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing"
        >
          <GripVertical className="w-3 h-3 text-gray-400" />
        </div>

        {/* 原始股票项 */}
        <StockListItem {...props} />
      </div>
    </div>
  );
}
