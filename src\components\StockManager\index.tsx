import { useState } from 'react';
import { Activity, Plus, Settings, X } from 'lucide-react';
import { useStockList } from '@/hooks/useStockList';
import { useStockGroups } from '@/hooks/useStockGroups';
import { StockInput } from './StockInput';
import { StockList } from './StockList';
import { GroupManager } from './GroupManager';

interface StockManagerProps {
  onSelectStock?: (code: string) => void;
  selectedStock?: string | null;
  isFullScreen?: boolean;
}

export function StockManager({ onSelectStock, selectedStock, isFullScreen = false }: StockManagerProps) {
  const {
    stocks,
    addStock,
    removeStock,
    isLoading,
  } = useStockList();
  const { groups } = useStockGroups();
  const [showRealTimeData] = useState(true);

  // 模态对话框状态
  const [showAddStockModal, setShowAddStockModal] = useState(false);
  const [showGroupManagerModal, setShowGroupManagerModal] = useState(false);

  // 处理添加股票
  const handleAddStock = async (code: string, name?: string, groupId?: string) => {
    const result = await addStock(code, name, groupId);
    if (result.success) {
      setShowAddStockModal(false);
    }
    return result;
  };

  // 处理删除股票
  const handleRemoveStock = (code: string) => {
    removeStock(code);

    // 如果删除的是当前选中的股票，清除选中状态
    if (selectedStock === code && onSelectStock) {
      onSelectStock('');
    }
  };

  return (
    <div className={`${isFullScreen ? 'w-full h-full bg-white flex flex-col' : 'card p-6 h-full flex flex-col'}`}>
      {/* 导航栏 */}
      <div className={`${isFullScreen ? 'mx-6 mb-6' : 'mb-6'} border-b border-gray-200 pb-4`}>
        <div className="flex items-center justify-between mb-4">
          <h2 className={`${isFullScreen ? 'text-xl' : 'text-lg'} font-semibold text-gray-900 flex items-center gap-2`}>
            <Activity className={`${isFullScreen ? 'w-6 h-6' : 'w-5 h-5'} text-blue-600`} />
            股票实时监控
          </h2>
          {stocks.length > 0 && showRealTimeData && (
            <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
              实时监控中 ({stocks.length} 只股票)
            </span>
          )}
        </div>

        {/* 导航按钮 */}
        <div className="flex gap-3">
          <button
            onClick={() => setShowAddStockModal(true)}
            className={`btn btn-primary flex items-center gap-2 ${isFullScreen ? 'px-4 py-2 text-base' : 'px-3 py-1.5 text-sm'}`}
          >
            <Plus className={`${isFullScreen ? 'w-5 h-5' : 'w-4 h-4'}`} />
            添加股票
          </button>
          <button
            onClick={() => setShowGroupManagerModal(true)}
            className={`btn btn-secondary flex items-center gap-2 ${isFullScreen ? 'px-4 py-2 text-base' : 'px-3 py-1.5 text-sm'}`}
          >
            <Settings className={`${isFullScreen ? 'w-5 h-5' : 'w-4 h-4'}`} />
            分组管理
          </button>
        </div>
      </div>

      {/* 股票实时状态显示区域 */}
      <div className={`${isFullScreen ? 'mx-6 flex-1 min-h-0' : 'flex-1 min-h-0'}`}>

        {/* 股票列表显示区域 */}
        <div className="bg-gray-50 rounded-lg p-4 h-full overflow-hidden">
          <StockList
            stocks={stocks}
            onRemoveStock={handleRemoveStock}
            onSelectStock={onSelectStock}
            selectedStock={selectedStock}
            showRealTimeData={showRealTimeData}
            isFullScreen={isFullScreen}
            enableGrouping={true}
          />
        </div>
      </div>

      {/* 添加股票模态对话框 */}
      {showAddStockModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            {/* 模态对话框头部 */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                <Plus className="w-5 h-5 text-blue-600" />
                添加股票
              </h3>
              <button
                onClick={() => setShowAddStockModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* 模态对话框内容 */}
            <div className="p-6">
              <StockInput
                onAddStock={handleAddStock}
                isLoading={isLoading}
                isFullScreen={false}
                groups={groups}
              />
            </div>

            {/* 模态对话框底部 */}
            <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
              <button
                onClick={() => setShowAddStockModal(false)}
                className="btn btn-secondary"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 分组管理模态对话框 */}
      {showGroupManagerModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
            {/* 模态对话框头部 */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                <Settings className="w-5 h-5 text-blue-600" />
                分组管理
              </h3>
              <button
                onClick={() => setShowGroupManagerModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* 模态对话框内容 */}
            <div className="overflow-y-auto max-h-[calc(80vh-140px)]">
              <GroupManager
                isFullScreen={false}
                onGroupSelect={(groupId) => {
                  // 可以在这里处理分组选择逻辑
                  console.log('选中分组:', groupId);
                }}
              />
            </div>

            {/* 模态对话框底部 */}
            <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
              <button
                onClick={() => setShowGroupManagerModal(false)}
                className="btn btn-secondary"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default StockManager;
